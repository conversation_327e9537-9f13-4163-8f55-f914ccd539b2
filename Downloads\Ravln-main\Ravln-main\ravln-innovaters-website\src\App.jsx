import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [activeSection, setActiveSection] = useState('home')
  const [isLoading, setIsLoading] = useState(true)
  const [loadingProgress, setLoadingProgress] = useState(0)

  // Loading animation effect
  useEffect(() => {
    const timer = setInterval(() => {
      setLoadingProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer)
          setTimeout(() => setIsLoading(false), 500)
          return 100
        }
        return prev + Math.random() * 15
      })
    }, 150)

    return () => clearInterval(timer)
  }, [])

  // Intersection Observer for scroll animations
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in')
        }
      })
    }, observerOptions)

    // Observe all sections after loading is complete
    if (!isLoading) {
      const sections = document.querySelectorAll('.animate-on-scroll')
      sections.forEach(section => observer.observe(section))
    }

    return () => observer.disconnect()
  }, [isLoading])

  const scrollToSection = (sectionId) => {
    setActiveSection(sectionId)
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  // Loading Screen Component
  if (isLoading) {
    return (
      <div className="loading-screen">
        <div className="loading-content">
          <div className="loading-logo">
            <div className="logo-animation">
              <span className="logo-icon">🚀</span>
              <h1 className="logo-text">Ravln Innovaters</h1>
            </div>
          </div>
          <div className="loading-bar-container">
            <div className="loading-bar">
              <div
                className="loading-progress"
                style={{ width: `${loadingProgress}%` }}
              ></div>
            </div>
            <p className="loading-text">
              {loadingProgress < 30 ? 'Initializing Innovation...' :
               loadingProgress < 60 ? 'Loading Creative Solutions...' :
               loadingProgress < 90 ? 'Preparing Your Experience...' :
               'Almost Ready!'}
            </p>
          </div>
          <div className="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="App">
      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <div className="nav-logo">
            <h2>🚀 Ravln Innovaters</h2>
          </div>
          <ul className="nav-menu">
            <li><a href="#home" onClick={() => scrollToSection('home')}>Home</a></li>
            <li><a href="#services" onClick={() => scrollToSection('services')}>Services</a></li>
            <li><a href="#about" onClick={() => scrollToSection('about')}>About</a></li>
            <li><a href="#projects" onClick={() => scrollToSection('projects')}>Projects</a></li>
            <li><a href="#mission" onClick={() => scrollToSection('mission')}>Mission</a></li>
            <li><a href="#contact" onClick={() => scrollToSection('contact')}>Contact</a></li>
          </ul>
        </div>
      </nav>

      {/* Home Section */}
      <section id="home" className="hero">
        <div className="hero-particles">
          <div className="particle"></div>
          <div className="particle"></div>
          <div className="particle"></div>
          <div className="particle"></div>
          <div className="particle"></div>
          <div className="particle"></div>
        </div>
        <div className="hero-content">
          <h1 className="hero-title">Empowering Ideas. Building Innovation.</h1>
          <p className="hero-subtitle">
            Welcome to <strong>Ravln Innovaters</strong> – where creativity meets technology.
            We bring your ideas to life through real-world solutions in software, IoT, and design.
          </p>
          <button className="cta-button" onClick={() => scrollToSection('services')}>
            Explore Our Services
          </button>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="services animate-on-scroll">
        <div className="container">
          <h2 className="section-title">🛠️ Our Services</h2>
          <div className="services-grid">
            <div className="service-card animate-card" data-delay="0">
              <div className="service-icon">💻</div>
              <h3>Web Development</h3>
              <p>MERN Stack, WordPress, E-commerce & Portfolio Sites</p>
            </div>
            <div className="service-card animate-card" data-delay="100">
              <div className="service-icon">📱</div>
              <h3>IoT & Embedded Projects</h3>
              <p>Arduino, Raspberry Pi, Smart Automation, College Mini Projects</p>
            </div>
            <div className="service-card animate-card" data-delay="200">
              <div className="service-icon">🎨</div>
              <h3>Custom Merchandise & Printing</h3>
              <p>Gang T-Shirts, Photo Frames, Branding Kits</p>
            </div>
            <div className="service-card animate-card" data-delay="300">
              <div className="service-icon">📊</div>
              <h3>Digital Marketing & SEO</h3>
              <p>Instagram growth, Google search ranking, Ad campaign strategy</p>
            </div>
            <div className="service-card animate-card" data-delay="400">
              <div className="service-icon">🤖</div>
              <h3>AI & Chatbot Integration</h3>
              <p>Educational and business-focused custom chatbots</p>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="about animate-on-scroll">
        <div className="container">
          <h2 className="section-title">👤 About Us</h2>
          <div className="about-content">
            <h3>Who We Are:</h3>
            <p>
              We are a team of young innovators passionate about technology, creativity, and solving real-world problems.
              Founded by students, <strong>Ravln Innovaters</strong> is a growing startup that provides innovative solutions
              across digital, hardware, and design domains.
            </p>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="projects animate-on-scroll">
        <div className="container">
          <h2 className="section-title">🔬 Recent Projects</h2>
          <div className="projects-grid">
            <div className="project-card animate-card" data-delay="0">
              <div className="project-icon">🔋</div>
              <h3>Smart Battery Charging System</h3>
              <p>Arduino + Dynamo + Solar integration</p>
            </div>
            <div className="project-card animate-card" data-delay="100">
              <div className="project-icon">📚</div>
              <h3>Library Management System</h3>
              <p>QR Scan + Arduino Rack Indicator</p>
            </div>
            <div className="project-card animate-card" data-delay="200">
              <div className="project-icon">🎓</div>
              <h3>AI Chatbot-Based Attendance System</h3>
              <p>Built with MERN Stack</p>
            </div>
            <div className="project-card animate-card" data-delay="300">
              <div className="project-icon">🌐</div>
              <h3>Custom Business Websites</h3>
              <p>For local brands & freelancers</p>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section id="mission" className="mission-vision animate-on-scroll">
        <div className="container">
          <div className="mission-vision-grid">
            <div className="mission-card animate-card" data-delay="0">
              <h2>🎯 Mission</h2>
              <p>
                To empower students and creators to turn their ideas into impactful projects by offering
                smart, scalable solutions in technology, design, and innovation.
              </p>
            </div>
            <div className="vision-card animate-card" data-delay="200">
              <h2>👁️ Vision</h2>
              <p>
                To become a leading force in student innovation, known for delivering high-quality digital,
                IoT, and creative services that make everyday life easier and smarter.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="contact animate-on-scroll">
        <div className="container">
          <h2 className="section-title">📞 Get In Touch</h2>
          <div className="contact-grid">
            <div className="contact-info">
              <div className="contact-item">
                <span className="contact-icon">📱</span>
                <div>
                  <strong>Hari Raj</strong><br />
                  93422 43448
                </div>
              </div>
              <div className="contact-item">
                <span className="contact-icon">📱</span>
                <div>
                  <strong>Nivetha</strong><br />
                  90423 87799
                </div>
              </div>
              <div className="contact-item">
                <span className="contact-icon">📱</span>
                <div>
                  <strong>Ragul</strong><br />
                  73059 73427
                </div>
              </div>
              <div className="contact-item">
                <span className="contact-icon">📧</span>
                <div>
                  <strong>Email</strong><br />
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
              </div>
              <div className="contact-item">
                <span className="contact-icon">📍</span>
                <div>
                  <strong>Location</strong><br />
                  Tamil Nadu, India
                </div>
              </div>
            </div>
            <div className="social-links">
              <h3>Follow Us</h3>
              <a href="https://instagram.com/ravln_innovaters" target="_blank" rel="noopener noreferrer">
                📸 @ravln_innovaters
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <p>Website Built By: Young minds with 💡 and 💻</p>
          <p>&copy; 2024 Ravln Innovaters. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}

export default App
